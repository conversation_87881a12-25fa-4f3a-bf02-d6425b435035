# app/models/document.py
from app import db
from datetime import datetime, timezone

# Define allowed document types
ALLOWED_DOCUMENT_TYPES = [
    "offerte",
    "werkbon",
    "onderhoudsbon",
    "onderhoudscontract",
    "meldkamercontract",
    "beveiligingscertificaat",
    "intakedocument",
    "projectietekening",
    "beveiligingsplan",
    "kabeltekeningen",
    "checklist oplevering installatie",
    "vrije_documenten",
    "factuur",
]

class Document(db.Model):
    __tablename__ = "documents"

    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey("customers.id"), nullable=False)
    event_id = db.Column(db.Integer, db.ForeignKey("events.id"), nullable=True)

    # File paths and URLs - increased size to accommodate Firebase Storage signed URLs
    file_url = db.Column(db.String(2000), nullable=False)
    file_path = db.Column(db.String(2000), nullable=False)
    name = db.Column(db.String(255), nullable=False)  # Original filename

    document_type = db.Column(db.String(50), nullable=False)
    uploaded_by = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=False)
    expiry_date = db.Column(db.DateTime, nullable=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    status = db.Column(db.String(20), nullable=True, default="active")  # Version status - null means no version management
    related_document_id = db.Column(db.Integer, db.ForeignKey("documents.id"), nullable=True)  # For linking sub-documents

    # Use string-based relationships to avoid circular imports
    customer = db.relationship("Customer", backref=db.backref("documents", lazy="dynamic"))
    # Remove the backref from Event relationship to avoid circular dependency
    event = db.relationship("Event", foreign_keys=[event_id])
    user = db.relationship("User", backref=db.backref("documents", lazy="dynamic"), foreign_keys=[uploaded_by])
    related_document = db.relationship("Document", remote_side=[id], backref="sub_documents")

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.document_type not in ALLOWED_DOCUMENT_TYPES:
            raise ValueError(f"Invalid document type. Must be one of {ALLOWED_DOCUMENT_TYPES}")

    def to_dict(self):
        # Calculate expiry status
        expiry_status = "green"
        if self.expiry_date:
            # Zorg ervoor dat expiry_date een tijdzone heeft
            expiry_date = self.expiry_date
            if expiry_date.tzinfo is None:
                expiry_date = expiry_date.replace(tzinfo=timezone.utc)

            days_until_expiry = (expiry_date - datetime.now(timezone.utc)).days
            if days_until_expiry <= 0:
                expiry_status = "red"
            elif days_until_expiry <= 60:
                expiry_status = "orange"
            else:
                expiry_status = "green"

        # Get customer name if customer relationship is loaded
        customer_name = None
        if hasattr(self, 'customer') and self.customer:
            customer_name = self.customer.name

        # Create the dictionary with all fields
        return {
            "id": self.id,
            "customer_id": self.customer_id,
            "customer_name": customer_name,
            "event_id": self.event_id,
            "file_url": self.file_url,
            "file_path": self.file_path,
            "name": self.name,
            "document_type": self.document_type,
            "uploaded_by": self.uploaded_by,
            "expiry_date": (self.expiry_date.replace(tzinfo=timezone.utc) if self.expiry_date.tzinfo is None else self.expiry_date).isoformat() if self.expiry_date else None,
            "expiry_status": expiry_status,
            "status": self.status,
            "created_at": self.created_at.isoformat(),
            "related_document_id": self.related_document_id,
            "sub_documents": []
        }
