# app/services/document_service.py
from app.repositories.document_repository import DocumentRepository
from app.repositories.event_repository import EventRepository
from app.utils.firebase import upload_file_to_storage, delete_file_from_storage, generate_signed_url
from app.models.document import Document
from app import db
from app.utils.cache_utils import clear_cache_for_entity
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class DocumentService:
    def __init__(self):
        self.document_repo = DocumentRepository()
        self.event_repo = EventRepository()

    def get_documents_by_customer(self, customer_id: int) -> List[Dict]:
        """
        Get all documents for a customer, including active and inactive,
        with their sub-documents and fresh signed URLs.
        """
        documents = self.document_repo.get_most_recent_by_customer(customer_id)
        result = []
        for doc in documents:
            doc_dict = doc.to_dict()

            # Refresh the signed URL for the main document
            doc_dict = self._refresh_signed_url(doc_dict)

            # Fetch sub-documents and refresh their signed URLs
            sub_documents = self.document_repo.get_all_by_related_document(doc.id)
            refreshed_sub_docs = []
            for sub_doc in sub_documents:
                sub_doc_dict = sub_doc.to_dict()
                sub_doc_dict = self._refresh_signed_url(sub_doc_dict)
                refreshed_sub_docs.append(sub_doc_dict)

            doc_dict["sub_documents"] = refreshed_sub_docs
            result.append(doc_dict)
        return result

    def _refresh_signed_url(self, document_dict: Dict) -> Dict:
        """
        Generate a fresh signed URL for a document if it has a file path (storage path).

        Args:
            document_dict: Document dictionary with file_path (which contains the Firebase Storage path)

        Returns:
            Dict: Document dictionary with refreshed file_url
        """
        try:
            # The database stores the Firebase Storage path in the 'file_path' field
            storage_path = document_dict.get('file_path')
            if storage_path:
                # Generate a fresh signed URL (expires in 1 hour)
                fresh_url = generate_signed_url(storage_path, expiration_seconds=3600)
                document_dict['file_url'] = fresh_url
                logger.info(f"Refreshed signed URL for document {document_dict.get('id')} using storage path: {storage_path}")
            else:
                logger.warning(f"No file_path (storage path) found for document {document_dict.get('id')}")
        except Exception as e:
            logger.error(f"Failed to refresh signed URL for document {document_dict.get('id')}: {str(e)}")
            # Don't raise exception, just log the error and continue with existing URL

        return document_dict

    def get_document_by_id(self, document_id: int) -> Dict:
        """Get a document by its ID with a fresh signed URL."""
        document = self.document_repo.get_by_id(document_id)
        if not document:
            raise Exception("Document not found")

        doc_dict = document.to_dict()

        # Refresh the signed URL for the main document
        doc_dict = self._refresh_signed_url(doc_dict)

        # Get and refresh signed URLs for sub-documents
        sub_documents = self.document_repo.get_all_by_related_document(document_id)
        refreshed_sub_docs = []
        for sub_doc in sub_documents:
            sub_doc_dict = sub_doc.to_dict()
            sub_doc_dict = self._refresh_signed_url(sub_doc_dict)
            refreshed_sub_docs.append(sub_doc_dict)

        doc_dict["sub_documents"] = refreshed_sub_docs
        return doc_dict

    def get_upcoming_expirations(self, page: int = 1, per_page: int = 20) -> Tuple[List[Document], int]:
        """
        Get documents that are expiring within the next 30 days.
        Only returns active documents.
        """
        try:
            documents, total = self.document_repo.get_upcoming_expirations(page, per_page)
            # Additional filter to ensure we only get active documents
            active_documents = [doc for doc in documents if doc.status == "active"]
            return active_documents, total
        except Exception as e:
            logger.error(f"Error in get_upcoming_expirations: {str(e)}")
            raise e

    def get_expired_documents(self, page: int = 1, per_page: int = 20) -> Tuple[List[Document], int]:
        """
        Get documents that have already expired.
        Only returns active documents.
        """
        try:
            documents, total = self.document_repo.get_expired_documents(page, per_page)
            # Additional filter to ensure we only get active documents
            active_documents = [doc for doc in documents if doc.status == "active"]
            return active_documents, total
        except Exception as e:
            logger.error(f"Error in get_expired_documents: {str(e)}")
            raise e

    def create_document(self, customer_id: int, event_id: Optional[int], file, document_type: str, uploaded_by: int, expiry_date: Optional[str] = None, related_document_id: Optional[int] = None, use_version_status: bool = True, version_status: str = "active") -> Dict:
        """
        Create a new document and mark any existing active documents of the same type as inactive.
        """
        if event_id:
            event = self.event_repo.get_by_id(event_id)
            if not event:
                raise Exception("Event not found")
            if event.event_type != document_type:
                raise Exception(f"Document type '{document_type}' must match event type '{event.event_type}'")

        # Use a more secure path structure with UUID to prevent guessing
        import uuid
        import os

        # Get the original file extension and ensure we have a valid filename
        original_filename = file.filename

        # Handle case where filename is None or empty
        if not original_filename or original_filename.strip() == '':
            # Generate a fallback filename based on document type and timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            original_filename = f"{document_type}_{timestamp}.pdf"
            logger.warning(f"File had no filename, using fallback: {original_filename}")

        file_ext = os.path.splitext(original_filename)[1].lower() if '.' in original_filename else '.pdf'

        secure_filename = f"{uuid.uuid4()}{file_ext}"
        destination_path = f"documents/{customer_id}/{document_type}/{secure_filename}"

        try:
            logger.info(f"Uploading file to Firebase Storage: {original_filename} -> {destination_path}")
            file_url, storage_path = upload_file_to_storage(file, destination_path)
            logger.info(f"File uploaded successfully. Storage path: {storage_path}, URL length: {len(file_url)}")
        except Exception as e:
            logger.error(f"Failed to upload file to Firebase Storage: {str(e)}")
            raise Exception(f"Failed to upload file: {str(e)}")

        expiry_date_dt = datetime.fromisoformat(expiry_date) if expiry_date else None

        try:
            # When use_version_status is False, we don't apply version management logic
            # but we still need to set a status for the database (use None to indicate no version management)
            if use_version_status:
                final_version_status = version_status

                # Check for existing active document of the same type only if using version status and new document is active
                if final_version_status == "active":
                    existing_documents = self.document_repo.get_all_by_customer(customer_id)
                    active_document = next(
                        (doc for doc in existing_documents
                         if doc.document_type == document_type and doc.status == "active"),
                        None
                    )

                    # If there's an active document, mark it as inactive
                    if active_document:
                        active_document.status = "inactive"
                        db.session.add(active_document)
                        logger.info(f"Marked old document ID {active_document.id} as inactive for customer ID {customer_id}, type {document_type}")
            else:
                # When version status is not used, set status to None to indicate no version management
                final_version_status = None

            document = self.document_repo.create(
                customer_id, event_id, file_url, storage_path, original_filename,
                document_type, uploaded_by, expiry_date_dt, related_document_id, final_version_status
            )
            db.session.commit()
            logger.info(f"Document created successfully: ID {document.id}")

            # Clear cache for related entities
            clear_cache_for_entity('customer_documents', customer_id)
            clear_cache_for_entity('document_expirations')

            return document.to_dict()
        except Exception as e:
            db.session.rollback()
            # Delete the uploaded file using the actual storage path
            delete_file_from_storage(storage_path)
            logger.error(f"Failed to create document: {str(e)}")
            raise Exception(f"Failed to create document: {str(e)}")



    def delete_document(self, document_id: int) -> bool:
        try:
            document = self.document_repo.get_by_id(document_id)
            if not document:
                logger.error(f"Error in delete_document: Document with ID {document_id} not found in database")
                raise Exception(f"Document with ID {document_id} not found")

            # If this is a beveiligingscertificaat, delete all sub-documents first
            if document.document_type == "beveiligingscertificaat":
                sub_documents = self.document_repo.get_all_by_related_document(document_id)
                for sub_doc in sub_documents:
                    delete_file_from_storage(sub_doc.file_path)
                    self.document_repo.delete(sub_doc.id)
                    logger.info(f"Deleted sub-document ID {sub_doc.id} for beveiligingscertificaat ID {document_id}")

            # Find and reactivate the previous document
            old_documents = self.document_repo.get_all_by_customer(document.customer_id)
            old_document = next(
                (doc for doc in old_documents
                 if doc.document_type == document.document_type
                 and doc.status == "inactive"
                 and doc.id != document.id),  # Exclude current document
                None
            )

            if old_document:
                old_document.status = "active"
                db.session.add(old_document)
                logger.info(f"Reactivating old document ID {old_document.id} after deleting document ID {document_id}")

            # Delete the current document
            delete_file_from_storage(document.file_path)
            self.document_repo.delete(document_id)

            # Commit all changes
            db.session.commit()

            # Clear cache for this document and related entities
            clear_cache_for_entity('document', document_id)
            clear_cache_for_entity('customer_documents', document.customer_id)
            clear_cache_for_entity('document_expirations')

            return True

        except Exception as e:
            db.session.rollback()
            logger.error(f"Error in delete_document: {str(e)}")
            raise e
