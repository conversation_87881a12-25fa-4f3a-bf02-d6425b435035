from firebase_admin import storage
import uuid
import os
import io
import logging

logger = logging.getLogger(__name__)

def upload_file_to_storage(file, destination_path: str) -> tuple[str, str]:
    """
    Upload a file to Firebase Storage with organized folder structure.

    Args:
        file: File object to upload
        destination_path: Desired path structure (e.g., "documents/123/offerte/filename.pdf")

    Returns:
        tuple: (signed_url, actual_storage_path)
    """
    try:
        # Generate a random UUID for the filename to prevent guessing
        file_extension = os.path.splitext(destination_path)[1]
        random_filename = f"{uuid.uuid4()}{file_extension}"

        # Keep the directory structure but use random filename
        directory = os.path.dirname(destination_path)
        secure_path = f"{directory}/{random_filename}" if directory else random_filename

        bucket = storage.bucket()
        blob = bucket.blob(secure_path)

        # Reset file pointer to beginning if it's a file-like object
        if hasattr(file, 'seek'):
            file.seek(0)

        blob.upload_from_file(file)
        logger.info(f"Successfully uploaded file to Firebase Storage: {secure_path}")

        # Generate a signed URL that expires after 24 hours
        # This ensures only authenticated users with the URL can access the file
        signed_url = blob.generate_signed_url(
            version="v4",
            expiration=86400,  # 24 hours in seconds
            method="GET"
        )

        return signed_url, secure_path
    except Exception as e:
        logger.error(f"Failed to upload file to Firebase Storage: {str(e)}")
        raise Exception(f"Failed to upload file to Firebase Storage: {str(e)}")

def upload_bytes_to_storage(file_bytes: bytes, destination_path: str, content_type: str = None) -> tuple[str, str]:
    """
    Upload bytes data to Firebase Storage.

    Args:
        file_bytes: Bytes data to upload
        destination_path: Desired path structure
        content_type: MIME type of the file

    Returns:
        tuple: (signed_url, actual_storage_path)
    """
    try:
        # Generate a random UUID for the filename to prevent guessing
        file_extension = os.path.splitext(destination_path)[1]
        random_filename = f"{uuid.uuid4()}{file_extension}"

        # Keep the directory structure but use random filename
        directory = os.path.dirname(destination_path)
        secure_path = f"{directory}/{random_filename}" if directory else random_filename

        bucket = storage.bucket()
        blob = bucket.blob(secure_path)

        if content_type:
            blob.content_type = content_type

        blob.upload_from_string(file_bytes)
        logger.info(f"Successfully uploaded bytes to Firebase Storage: {secure_path}")

        # Generate a signed URL that expires after 24 hours
        signed_url = blob.generate_signed_url(
            version="v4",
            expiration=86400,  # 24 hours in seconds
            method="GET"
        )

        return signed_url, secure_path
    except Exception as e:
        logger.error(f"Failed to upload bytes to Firebase Storage: {str(e)}")
        raise Exception(f"Failed to upload bytes to Firebase Storage: {str(e)}")

def delete_file_from_storage(file_path: str) -> None:
    """
    Delete a file from Firebase Storage.

    Args:
        file_path: Path to the file in Firebase Storage
    """
    try:
        bucket = storage.bucket()
        blob = bucket.blob(file_path)

        # Check if the file exists before trying to delete it
        if blob.exists():
            blob.delete()
            logger.info(f"Successfully deleted file from Firebase Storage: {file_path}")
        else:
            logger.info(f"File does not exist in Firebase Storage, skipping deletion: {file_path}")
    except Exception as e:
        logger.error(f"Failed to delete file from Firebase Storage: {str(e)}")
        # Don't raise exception for delete operations to avoid breaking the flow

def generate_signed_url(storage_path: str, expiration_seconds: int = 3600) -> str:
    """
    Generate a fresh signed URL for a file in Firebase Storage.

    Args:
        storage_path: Path to the file in Firebase Storage
        expiration_seconds: URL expiration time in seconds (default: 1 hour)

    Returns:
        str: Signed URL for the file

    Raises:
        Exception: If the file doesn't exist or URL generation fails
    """
    try:
        bucket = storage.bucket()
        blob = bucket.blob(storage_path)

        # Check if the file exists
        if not blob.exists():
            raise Exception(f"File does not exist in Firebase Storage: {storage_path}")

        # Generate a fresh signed URL
        signed_url = blob.generate_signed_url(
            version="v4",
            expiration=expiration_seconds,
            method="GET"
        )

        logger.info(f"Generated fresh signed URL for: {storage_path}")
        return signed_url
    except Exception as e:
        logger.error(f"Failed to generate signed URL for {storage_path}: {str(e)}")
        raise Exception(f"Failed to generate signed URL: {str(e)}")