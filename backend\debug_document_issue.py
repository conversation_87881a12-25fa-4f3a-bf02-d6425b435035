#!/usr/bin/env python3
"""
Debug script to identify why document preview and download are failing.
This script will test each step of the document retrieval process.
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.services.document_service import DocumentService
from app.repositories.document_repository import DocumentRepository
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_database_documents():
    """Check if there are any documents in the database."""
    logger.info("=== Testing Database Documents ===")
    
    app_result = create_app()
    app = app_result[0] if isinstance(app_result, tuple) else app_result
    
    with app.app_context():
        try:
            repo = DocumentRepository()
            
            # Get all documents
            documents = repo.get_all()
            logger.info(f"Total documents in database: {len(documents)}")
            
            if documents:
                for doc in documents[:5]:  # Show first 5 documents
                    logger.info(f"Document ID: {doc.id}")
                    logger.info(f"  Name: {doc.name}")
                    logger.info(f"  Customer ID: {doc.customer_id}")
                    logger.info(f"  File URL: {doc.file_url[:100]}..." if doc.file_url else "No file_url")
                    logger.info(f"  File Path: {doc.file_path}")
                    logger.info(f"  Document Type: {doc.document_type}")
                    logger.info(f"  Status: {doc.status}")
                    logger.info("---")
                
                return documents[0].id if documents else None
            else:
                logger.warning("No documents found in database!")
                return None
                
        except Exception as e:
            logger.error(f"Error accessing database: {str(e)}")
            return None

def test_document_service_retrieval(document_id):
    """Test document service retrieval."""
    logger.info(f"=== Testing Document Service Retrieval for ID {document_id} ===")
    
    app_result = create_app()
    app = app_result[0] if isinstance(app_result, tuple) else app_result
    
    with app.app_context():
        try:
            service = DocumentService()
            
            # Test getting document by ID
            document = service.get_document_by_id(document_id)
            
            logger.info(f"Document retrieved successfully:")
            logger.info(f"  ID: {document.get('id')}")
            logger.info(f"  Name: {document.get('name')}")
            logger.info(f"  File URL: {document.get('file_url', 'NOT FOUND')[:100]}..." if document.get('file_url') else "NO FILE_URL")
            logger.info(f"  File Path: {document.get('file_path', 'NOT FOUND')}")
            logger.info(f"  Customer ID: {document.get('customer_id')}")
            
            return document
            
        except Exception as e:
            logger.error(f"Error retrieving document: {str(e)}")
            return None

def test_firebase_file_exists(file_path):
    """Test if file exists in Firebase Storage."""
    logger.info(f"=== Testing Firebase Storage File Existence ===")
    
    try:
        from firebase_admin import storage
        
        bucket = storage.bucket()
        blob = bucket.blob(file_path)
        
        exists = blob.exists()
        logger.info(f"File exists in Firebase Storage: {exists}")
        
        if exists:
            # Get file metadata
            blob.reload()
            logger.info(f"File size: {blob.size} bytes")
            logger.info(f"Content type: {blob.content_type}")
            logger.info(f"Created: {blob.time_created}")
        
        return exists
        
    except Exception as e:
        logger.error(f"Error checking Firebase Storage: {str(e)}")
        return False

def test_signed_url_generation(file_path):
    """Test signed URL generation."""
    logger.info(f"=== Testing Signed URL Generation ===")
    
    try:
        from app.utils.firebase import generate_signed_url
        
        signed_url = generate_signed_url(file_path, expiration_seconds=3600)
        logger.info(f"Signed URL generated successfully")
        logger.info(f"URL length: {len(signed_url)}")
        logger.info(f"URL starts with: {signed_url[:100]}...")
        
        return signed_url
        
    except Exception as e:
        logger.error(f"Error generating signed URL: {str(e)}")
        return None

def test_url_accessibility(signed_url):
    """Test if the signed URL is accessible."""
    logger.info(f"=== Testing URL Accessibility ===")
    
    try:
        import requests
        
        response = requests.get(signed_url, timeout=10)
        logger.info(f"HTTP Status: {response.status_code}")
        logger.info(f"Content-Length: {len(response.content)}")
        logger.info(f"Content-Type: {response.headers.get('content-type', 'Not specified')}")
        
        if response.status_code == 200:
            logger.info("✅ URL is accessible and returns content")
            return True
        else:
            logger.error(f"❌ URL returned error status: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"Error accessing URL: {str(e)}")
        return False

def main():
    """Run comprehensive debugging."""
    print("=" * 60)
    print("DOCUMENT ISSUE DEBUGGING")
    print("=" * 60)
    
    # Step 1: Check database
    document_id = test_database_documents()
    if not document_id:
        print("\n❌ No documents found in database. Upload a document first!")
        return False
    
    # Step 2: Test document service
    document = test_document_service_retrieval(document_id)
    if not document:
        print(f"\n❌ Failed to retrieve document {document_id} from service!")
        return False
    
    file_path = document.get('file_path')
    if not file_path:
        print(f"\n❌ Document {document_id} has no file_path!")
        return False
    
    # Step 3: Check Firebase Storage
    file_exists = test_firebase_file_exists(file_path)
    if not file_exists:
        print(f"\n❌ File does not exist in Firebase Storage: {file_path}")
        return False
    
    # Step 4: Test signed URL generation
    signed_url = test_signed_url_generation(file_path)
    if not signed_url:
        print(f"\n❌ Failed to generate signed URL for: {file_path}")
        return False
    
    # Step 5: Test URL accessibility
    url_accessible = test_url_accessibility(signed_url)
    if not url_accessible:
        print(f"\n❌ Generated signed URL is not accessible!")
        return False
    
    print("\n" + "=" * 60)
    print("✅ ALL TESTS PASSED!")
    print("The document storage system appears to be working correctly.")
    print("The issue might be with:")
    print("1. Frontend authentication token")
    print("2. CORS settings")
    print("3. Network connectivity")
    print("4. Browser security settings")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n💥 CRITICAL ERROR: {str(e)}")
        sys.exit(1)
